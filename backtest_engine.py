#!/usr/bin/env python3
"""
Backtest Engine Module
Handles backtesting operations across multiple symbols and timeframes.
"""

from typing import Dict, Any, List
import pandas as pd

from core_components import run_backtest
from metrics import calc_metrics, print_metrics
from base import BaseStrategy


class BacktestEngine:
    """Handles backtesting operations for single and multi-timeframe strategies."""
    
    def __init__(self, strategy: BaseStrategy):
        """Initialize backtest engine.
        
        Args:
            strategy: Strategy instance to backtest
        """
        self.strategy = strategy
    
    def run_single_backtest(self, symbol: str, timeframe: str, 
                          data: pd.DataFrame) -> Dict[str, Any]:
        """Run backtest on a single symbol and timeframe.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe string
            data: OHLC data
            
        Returns:
            Dictionary containing portfolio and metrics
        """
        try:
            tf_data = {timeframe: data}
            signals = self.strategy.generate_signals(tf_data)
            print(f"✅ Signals generated for {symbol} {timeframe}")
            
            # Run backtest
            portfolio = run_backtest(data, signals)
            
            # Calculate metrics
            metrics = calc_metrics(portfolio)
            
            result = {
                'portfolio': portfolio,
                'metrics': metrics
            }
            
            print_metrics(metrics, f"{symbol} {timeframe}")
            return result
            
        except Exception as e:
            print(f"⚠️ Backtest failed for {symbol} {timeframe}: {e}")
            raise
    
    def run_multi_timeframe_backtest(self, symbol: str, timeframes: Dict[str, pd.DataFrame],
                                   required_tfs: List[str], primary_tf: str) -> Dict[str, Any]:
        """Run backtest for multi-timeframe strategy.
        
        Args:
            symbol: Trading symbol
            timeframes: Dictionary of timeframe -> DataFrame
            required_tfs: List of required timeframes
            primary_tf: Primary timeframe for backtesting
            
        Returns:
            Dictionary containing portfolio and metrics
        """
        if primary_tf not in timeframes:
            raise ValueError(f"Primary timeframe {primary_tf} not available for {symbol}")
        
        try:
            # Provide all available timeframes to the strategy
            tf_data = {}
            for req_tf in required_tfs:
                if req_tf in timeframes:
                    tf_data[req_tf] = timeframes[req_tf]
            
            signals = self.strategy.generate_signals(tf_data)
            print(f"✅ Multi-timeframe signals generated for {symbol}")
            
            # Run backtest on primary timeframe
            primary_data = timeframes[primary_tf]
            portfolio = run_backtest(primary_data, signals)
            
            # Calculate metrics
            metrics = calc_metrics(portfolio)
            
            result = {
                'portfolio': portfolio,
                'metrics': metrics
            }
            
            print_metrics(metrics, f"{symbol} {primary_tf} (Multi-TF)")
            return result
            
        except Exception as e:
            print(f"⚠️ Multi-timeframe backtest failed for {symbol}: {e}")
            raise
    
    def run_full_backtest(self, data: Dict[str, Dict[str, pd.DataFrame]]) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Run full backtest on all symbols and timeframes.
        
        Args:
            data: Multi-timeframe data dictionary
            
        Returns:
            Nested dictionary of results: symbol -> timeframe -> results
        """
        print("Running full backtest on all symbols and timeframes...")
        results = {}
        
        # Check if strategy requires multiple timeframes
        required_tfs = self.strategy.get_required_timeframes()
        is_multi_tf = len(required_tfs) > 1
        
        for symbol, timeframes in data.items():
            results[symbol] = {}
            
            if is_multi_tf:
                # Multi-timeframe strategy - use primary timeframe for backtesting
                primary_tf = self.strategy.parameters.get('primary_timeframe', required_tfs[0])
                
                try:
                    result = self.run_multi_timeframe_backtest(
                        symbol, timeframes, required_tfs, primary_tf
                    )
                    results[symbol][primary_tf] = result
                    
                except Exception as e:
                    print(f"⚠️ Multi-timeframe backtest failed for {symbol}: {e}")
                    results[symbol][primary_tf] = None
            else:
                # Single timeframe strategy - run on all available timeframes
                for timeframe, df in timeframes.items():
                    try:
                        result = self.run_single_backtest(symbol, timeframe, df)
                        results[symbol][timeframe] = result
                        
                    except Exception as e:
                        print(f"⚠️ Backtest failed for {symbol} {timeframe}: {e}")
                        results[symbol][timeframe] = None
        
        return results
    
    def get_backtest_summary(self, results: Dict[str, Dict[str, Dict[str, Any]]]) -> Dict[str, Any]:
        """Generate summary statistics from backtest results.
        
        Args:
            results: Backtest results dictionary
            
        Returns:
            Summary statistics dictionary
        """
        summary = {
            'total_symbols': 0,
            'total_timeframes': 0,
            'successful_backtests': 0,
            'failed_backtests': 0,
            'avg_sharpe': 0.0,
            'avg_return': 0.0,
            'best_performer': None,
            'worst_performer': None
        }
        
        all_metrics = []
        performance_data = []
        
        for symbol, timeframes in results.items():
            summary['total_symbols'] += 1
            
            for timeframe, result in timeframes.items():
                summary['total_timeframes'] += 1
                
                if result is not None and 'metrics' in result:
                    summary['successful_backtests'] += 1
                    metrics = result['metrics']
                    all_metrics.append(metrics)
                    
                    performance_data.append({
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'sharpe': metrics.get('sharpe', 0),
                        'return': metrics.get('return', 0)
                    })
                else:
                    summary['failed_backtests'] += 1
        
        if all_metrics:
            # Calculate averages
            summary['avg_sharpe'] = sum(m.get('sharpe', 0) for m in all_metrics) / len(all_metrics)
            summary['avg_return'] = sum(m.get('return', 0) for m in all_metrics) / len(all_metrics)
            
            # Find best and worst performers
            if performance_data:
                best = max(performance_data, key=lambda x: x['sharpe'])
                worst = min(performance_data, key=lambda x: x['sharpe'])
                
                summary['best_performer'] = f"{best['symbol']} {best['timeframe']} (Sharpe: {best['sharpe']:.3f})"
                summary['worst_performer'] = f"{worst['symbol']} {worst['timeframe']} (Sharpe: {worst['sharpe']:.3f})"
        
        return summary
    
    def print_backtest_summary(self, results: Dict[str, Dict[str, Dict[str, Any]]]) -> None:
        """Print formatted backtest summary.
        
        Args:
            results: Backtest results dictionary
        """
        summary = self.get_backtest_summary(results)
        
        print("\n" + "="*50)
        print("📊 BACKTEST SUMMARY")
        print("="*50)
        print(f"Total Symbols: {summary['total_symbols']}")
        print(f"Total Timeframes: {summary['total_timeframes']}")
        print(f"Successful Backtests: {summary['successful_backtests']}")
        print(f"Failed Backtests: {summary['failed_backtests']}")
        
        if summary['successful_backtests'] > 0:
            print(f"Average Sharpe Ratio: {summary['avg_sharpe']:.3f}")
            print(f"Average Return: {summary['avg_return']:.2f}%")
            
            if summary['best_performer']:
                print(f"Best Performer: {summary['best_performer']}")
            if summary['worst_performer']:
                print(f"Worst Performer: {summary['worst_performer']}")
        
        print("="*50)
